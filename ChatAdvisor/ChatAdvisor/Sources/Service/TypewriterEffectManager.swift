//
//  TypewriterEffectManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-03.
//

import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "TypewriterEffectManager")

/// 打字机效果状态
enum TypewriterState {
    case idle           // 空闲
    case typing         // 正在打字
    case paused         // 暂停
    case completed      // 完成
}

/// 打字机效果配置
struct TypewriterConfig {
    let baseSpeed: TimeInterval = 0.02        // 基础打字速度（秒/字符）- 优化：从0.03改为0.02
    let fastSpeed: TimeInterval = 0.008       // 快速打字速度 - 优化：从0.01改为0.008
    let slowSpeed: TimeInterval = 0.06        // 慢速打字速度 - 优化：从0.08改为0.06
    let punctuationDelay: TimeInterval = 0.1  // 标点符号后的延迟 - 优化：从0.15改为0.1
    let sentenceDelay: TimeInterval = 0.2     // 句子结束后的延迟 - 优化：从0.3改为0.2
    let maxBatchSize: Int = 2                 // 最大批量显示字符数 - 优化：从5改为2
    let smoothingEnabled: Bool = true         // 是否启用平滑效果
}

/// 打字机效果管理器
class TypewriterEffectManager: ObservableObject {
    @Published var displayedContent: String = ""
    @Published var state: TypewriterState = .idle
    @Published var progress: Double = 0.0
    
    private var contentQueue: [String] = []
    private var fullContent: String = ""
    private var currentIndex: String.Index
    private var typingTimer: Timer?
    private var config = TypewriterConfig()
    private let contentLock = NSLock()
    
    // 性能优化相关
    private var lastUpdateTime = Date()
    private var pendingContent: String = ""
    private let updateThrottleInterval: TimeInterval = 0.05

    // 性能监控
    private var startTime: Date?
    private var totalCharactersProcessed: Int = 0
    private var updateCount: Int = 0
    
    init() {
        self.currentIndex = "".startIndex
    }
    
    deinit {
        stopTyping()
    }
    
    // MARK: - 公开方法
    
    /// 添加新内容到队列
    @MainActor func appendContent(_ newContent: String) {
        contentLock.lock()
        defer { contentLock.unlock() }

        guard !newContent.isEmpty else { return }

        contentQueue.append(newContent)
        fullContent += newContent

        // 如果当前空闲，开始打字
        if state == .idle {
            startTyping()
        } else if state == .typing {
            // 如果正在打字，确保currentIndex不会超出新的fullContent范围
            if currentIndex > fullContent.endIndex {
                currentIndex = fullContent.endIndex
            }
        }

        logger.debug("添加内容到打字机队列: \(newContent.count) 字符，总长度: \(self.fullContent.count)")
    }
    
    /// 设置完整内容（用于一次性设置）
    @MainActor func setContent(_ content: String) {
        contentLock.lock()
        defer { contentLock.unlock() }
        
        stopTyping()
        fullContent = content
        contentQueue = [content]
        displayedContent = ""
        currentIndex = fullContent.startIndex
        progress = 0.0
        
        if !content.isEmpty {
            startTyping()
        }
    }
    
    /// 立即完成打字效果
    @MainActor func completeImmediately() {
        stopTyping()
        displayedContent = fullContent
        progress = 1.0
        state = .completed
        logger.info("打字机效果立即完成")
    }
    
    /// 暂停打字效果
    func pauseTyping() {
        guard state == .typing else { return }
        
        typingTimer?.invalidate()
        typingTimer = nil
        state = .paused
        logger.debug("打字机效果已暂停")
    }
    
    /// 恢复打字效果
    func resumeTyping() {
        guard state == .paused else { return }
        
        state = .typing
        scheduleNextCharacter()
        logger.debug("打字机效果已恢复")
    }
    
    /// 重置打字机状态
    @MainActor func reset() {
        stopTyping()
        contentLock.lock()
        defer { contentLock.unlock() }
        
        displayedContent = ""
        fullContent = ""
        contentQueue.removeAll()
        currentIndex = "".startIndex
        progress = 0.0
        state = .idle
        pendingContent = ""
    }
    
    // MARK: - 私有方法
    
    private func startTyping() {
        guard !fullContent.isEmpty else { return }

        state = .typing
        currentIndex = fullContent.startIndex

        // 性能监控
        startTime = Date()
        totalCharactersProcessed = 0
        updateCount = 0

        scheduleNextCharacter()
        logger.debug("开始打字机效果")
    }
    
    
    private func stopTyping() {
        typingTimer?.invalidate()
        typingTimer = nil
        if state == .typing || state == .paused {
            state = .idle
        }
    }
    
    private func scheduleNextCharacter() {
        guard state == .typing, currentIndex < fullContent.endIndex else {
            // 打字完成
            state = .completed
            progress = 1.0
            logger.debug("打字机效果完成")
            return
        }

        // 确保之前的Timer已经清理
        typingTimer?.invalidate()

        let delay = calculateDelay()

        typingTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] timer in
            // 验证Timer状态，避免重复执行
            guard timer.isValid else { return }
            Task { @MainActor in
                self?.typeNextCharacter()
            }
        }
    }
    
    @MainActor private func typeNextCharacter() {
        // 验证状态，确保仍在打字模式
        guard state == .typing, currentIndex < fullContent.endIndex else {
            logger.debug("typeNextCharacter: 状态检查失败，停止打字")
            return
        }

        // 批量处理字符以提高性能，但确保不超出边界
        let remainingChars = fullContent.distance(from: currentIndex, to: fullContent.endIndex)
        let batchSize = min(config.maxBatchSize, remainingChars)

        guard batchSize > 0 else {
            logger.debug("typeNextCharacter: 无剩余字符")
            return
        }

        let endIndex = fullContent.index(currentIndex, offsetBy: batchSize)
        let newChars = String(fullContent[currentIndex..<endIndex])

        displayedContent += newChars
        currentIndex = endIndex

        // 更新进度
        let totalLength = fullContent.count
        let displayedLength = displayedContent.count
        progress = totalLength > 0 ? Double(displayedLength) / Double(totalLength) : 1.0

        // 性能监控
        totalCharactersProcessed += newChars.count
        updateCount += 1

        // 继续下一个字符
        scheduleNextCharacter()
    }
    
    private func calculateDelay() -> TimeInterval {
        guard currentIndex < fullContent.endIndex else { return config.baseSpeed }
        
        let currentChar = fullContent[currentIndex]
        
        // 根据字符类型调整延迟
        if currentChar.isPunctuation {
            if currentChar == "." || currentChar == "!" || currentChar == "?" || currentChar == "。" || currentChar == "！" || currentChar == "？" {
                return config.sentenceDelay
            } else {
                return config.punctuationDelay
            }
        } else if currentChar.isWhitespace {
            return config.fastSpeed
        } else {
            return config.baseSpeed
        }
    }
}

/// 智能断点检测器
extension TypewriterEffectManager {
    
    /// 检查是否应该在当前位置暂停
    private func shouldPauseAtCurrentPosition() -> Bool {
        guard currentIndex < fullContent.endIndex else { return false }
        
        let currentChar = fullContent[currentIndex]
        
        // 在句子结束处暂停
        if currentChar == "." || currentChar == "!" || currentChar == "?" || 
           currentChar == "。" || currentChar == "！" || currentChar == "？" {
            return true
        }
        
        // 在代码块结束处暂停
        if currentChar == "}" || currentChar == "]" {
            return true
        }
        
        return false
    }
    
    /// 智能速度调节
    private func getAdaptiveSpeed() -> TimeInterval {
        let recentContent = getRecentContent(length: 20)
        
        // 如果最近的内容主要是代码，使用快速模式
        if isCodeContent(recentContent) {
            return config.fastSpeed
        }
        
        // 如果是普通文本，使用正常速度
        return config.baseSpeed
    }
    
    private func getRecentContent(length: Int) -> String {
        let startIndex = max(0, displayedContent.count - length)
        let endIndex = displayedContent.count
        return String(displayedContent.suffix(length))
    }
    
    private func isCodeContent(_ content: String) -> Bool {
        let codeIndicators = ["{", "}", "[", "]", "(", ")", ";", "=", "->", "=>"]
        let indicatorCount = codeIndicators.reduce(0) { count, indicator in
            count + content.components(separatedBy: indicator).count - 1
        }
        
        return indicatorCount > content.count / 10 // 如果代码指示符超过10%，认为是代码
    }

    /// 获取性能统计信息
    func getPerformanceStats() -> (duration: TimeInterval, charactersPerSecond: Double, updatesPerSecond: Double) {
        guard let startTime = startTime else {
            return (0, 0, 0)
        }

        let duration = Date().timeIntervalSince(startTime)
        let charactersPerSecond = duration > 0 ? Double(totalCharactersProcessed) / duration : 0
        let updatesPerSecond = duration > 0 ? Double(updateCount) / duration : 0

        return (duration, charactersPerSecond, updatesPerSecond)
    }
}
